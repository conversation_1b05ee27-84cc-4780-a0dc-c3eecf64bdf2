const { query, transaction } = require('../config/database');
const { hashPassword, comparePassword } = require('../utils/bcrypt');

class User {

  // 创建用户
  static async create(userData) {
    const { username, email, password, mp_open_id } = userData;
    // 生成UUID作为用户ID
    const { v4: uuidv4 } = require('uuid');
    const id = uuidv4();

    // 加密密码
    const hashedPassword = await hashPassword(password);

    const sql = 'INSERT INTO user (id, username, email, password, mp_open_id) VALUES (?, ?, ?, ?, ?)';

    try {
      await query(sql, [id, username, email, hashedPassword, mp_open_id]);
      return {
        id,
        username,
        email,
        mp_open_id,
        create_time: new Date()
      };
    } catch (error) {
      console.error('创建用户失败:', error.message);
      throw error;
    }
  }

  // 根据ID查找用户
  static async findById(id) {
    const sql = 'SELECT id, username, email, mp_open_id, create_time, update_time FROM user WHERE id = ?';

    try {
      const users = await query(sql, [id]);
      return users[0] || null;
    } catch (error) {
      console.error('查找用户失败:', error.message);
      throw error;
    }
  }

  // 根据邮箱查找用户
  static async findByEmail(email) {
    const sql = 'SELECT id, username, email, password, mp_open_id, create_time, update_time FROM user WHERE email = ?';

    try {
      const users = await query(sql, [email]);
      return users[0] || null;
    } catch (error) {
      console.error('查找用户失败:', error.message);
      throw error;
    }
  }

  // 获取所有用户（支持筛选）
  static async findAll(page = 1, pageSize = 10, filters = {}) {
    const offset = (page - 1) * pageSize;

    try {
      // 构建筛选条件
      let whereConditions = [];
      let queryParams = [];

      if (filters.username) {
        whereConditions.push('u.username LIKE ?');
        queryParams.push(`%${filters.username}%`);
      }

      if (filters.email) {
        whereConditions.push('u.email LIKE ?');
        queryParams.push(`%${filters.email}%`);
      }

      if (filters.roleName) {
        whereConditions.push('r.name LIKE ?');
        queryParams.push(`%${filters.roleName}%`);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // 获取总数的SQL
      const countSql = `
        SELECT COUNT(DISTINCT u.id) as total
        FROM user u
        ${filters.roleName ? 'LEFT JOIN user_role ur ON u.id = ur.user_id LEFT JOIN role r ON ur.role_id = r.id' : ''}
        ${whereClause}
      `;

      // 获取用户列表的SQL（包含角色信息）
      const userSql = `
        SELECT DISTINCT u.id, u.username, u.email, u.mp_open_id, u.create_time, u.update_time
        FROM user u
        ${filters.roleName ? 'LEFT JOIN user_role ur ON u.id = ur.user_id LEFT JOIN role r ON ur.role_id = r.id' : ''}
        ${whereClause}
        ORDER BY u.create_time DESC
        LIMIT ? OFFSET ?
      `;

      // 执行查询
      const countResult = await query(countSql, queryParams);
      const total = countResult[0].total;

      const users = await query(userSql, [...queryParams, pageSize, offset]);

      // 为每个用户获取角色信息
      const usersWithRoles = await Promise.all(users.map(async (user) => {
        const rolesSql = `
          SELECT r.id, r.name, r.code
          FROM role r
          INNER JOIN user_role ur ON r.id = ur.role_id
          WHERE ur.user_id = ?
        `;
        const roles = await query(rolesSql, [user.id]);
        return {
          ...user,
          roles: roles
        };
      }));

      return {
        list: usersWithRoles,
        total: total,
        page: page,
        pageSize: pageSize
      };
    } catch (error) {
      console.error('获取用户列表失败:', error.message);
      throw error;
    }
  }

  // 更新用户
  static async update(id, userData) {
    const { username, email, mp_open_id } = userData;
    const sql = 'UPDATE user SET username = ?, email = ?, mp_open_id = ? WHERE id = ?';

    try {
      const result = await query(sql, [username, email, mp_open_id, id]);
      if (result.affectedRows === 0) {
        return null;
      }
      return await this.findById(id);
    } catch (error) {
      console.error('更新用户失败:', error.message);
      throw error;
    }
  }

  // 删除用户
  static async delete(id) {
    const sql = 'DELETE FROM user WHERE id = ?';

    try {
      const result = await query(sql, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除用户失败:', error.message);
      throw error;
    }
  }

  // 验证用户登录
  static async validateLogin(email, password) {
    try {
      // 查找用户（包含密码）
      const user = await this.findByEmail(email);
      if (!user) {
        return null;
      }

      // 验证密码
      const isPasswordValid = await comparePassword(password, user.password);
      if (!isPasswordValid) {
        return null;
      }

      // 返回用户信息（不包含密码）
      const { password: _, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      console.error('用户登录验证失败:', error.message);
      throw error;
    }
  }

  // 更新密码
  static async updatePassword(id, newPassword) {
    try {
      const hashedPassword = await hashPassword(newPassword);
      const sql = 'UPDATE user SET password = ? WHERE id = ?';
      const result = await query(sql, [hashedPassword, id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新密码失败:', error.message);
      throw error;
    }
  }
}

module.exports = User;
